.work-table {
    background: #fff;
    border-radius: 5px;
    overflow: hidden;
}

.work-table thead {
    background: #f3f6f9;
}

.work-table th {
    font-size: 1.1rem !important;
    color: #495057;
    border: none;
    text-transform: capitalize;
}

.work-table td {
    padding: 1rem 0.75rem;
    border: none;
}

.work-table tbody tr:hover {
    background-color: #f8f9ff;
    transition: all 0.2s ease;
}

.category-header {
    background: #e1f0ff;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    padding: 0.75rem 1rem;
}

.line-item {
    border-bottom: 1px solid #ebedf3 !important;
}

.line-item td:first-child {
    font-weight: 600;
}

.percentage {
    font-weight: 600;
    font-size: 0.9rem;
    color: #fff;
    text-align: center;
    text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
}

.tooltipClass {
    font-size: 1rem;
}

.note-btn {
    display: contents;
    padding: 5px 7px !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.note-btn i {
    padding: 0 !important;
}

.lender-note-btn {
    display: contents;
    padding: 0 !important;
    margin-left: calc(0.5rem - 0.25rem);
}

.lender-note-btn i {
    padding: 0 !important;
}

.lender-notes-modal .modal-dialog {
    max-width: 500px;
}

.lender-notes-modal .modal-body {
    padding: 20px;
}

.lender-notes-textarea {
    width: 100%;
    min-height: 120px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
    resize: vertical;
    font-family: inherit;
}

.lender-notes-textarea:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: 0;
}

.lender-notes-modal .modal-footer {
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

.lender-notes-modal .modal-header {
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
}

.lender-notes-modal .modal-title {
    font-size: 1.1rem;
    font-weight: 600;
}

span.badge {
    font-size: 1rem;
}

.col-reject-reason {
    padding: 0 !important;
}

/* ----------------------- */
/* History table styles */
/* ----------------------- */
.history-table {
    margin-top: 2rem;
}

.history-table h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.history-table .badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.history-table .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.history-table .badge-success {
    background-color: #28a745;
    color: #fff;
}

.history-table .badge-danger {
    background-color: #dc3545;
    color: #fff;
}

.history-table .text-muted {
    color: #6c757d !important;
    font-style: italic;
}
.table-striped tbody tr {
    background-color: #fff !important;
}

/* Draw Summary */
.summary-section {
    background: #fff;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-section h4 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
    position: relative;
}

.summary-item:hover .copy-icon {
    opacity: 1;
    visibility: visible;
}

.summary-label {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 1rem;
    font-weight: 500;
    padding-left: 5px;
    position: relative;
}

.copy-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6c757d;
    padding: 2px 5px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.9);
}

.copy-icon:hover {
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}

/* Hide number input spinners/steppers */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
    appearance: textfield;
}

.doc-thumbnail-container {
    width: auto;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px solid #ddd;
    position: relative;
}

.doc-thumbnail-container img {
    max-width: 150px;
    height: 100px;
    object-fit: cover;
    cursor: pointer;
    z-index: 1;
}
